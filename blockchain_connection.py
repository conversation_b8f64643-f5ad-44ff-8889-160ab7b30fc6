from web3 import Web3

# Configuration
RPC_URL = "http://*************:24376/051b6a42-cf72-4bb0-85a7-7f838f931466"
PRIVATE_KEY = "7c6715808fa7f155c5f7ee985b235689b852da7d35dbf3b8093e4f3f3657244c"
WALLET_ADDRESS = "******************************************"
CONTRACT_ADDRESS = "******************************************"

def connect_to_blockchain():
    """Connect to the blockchain node"""
    w3 = Web3(Web3.HTTPProvider(RPC_URL))
    
    if w3.is_connected():
        print("Connected to blockchain node")
        return w3
    else:
        print("Connection failed")
        return None

def get_wallet_balance(w3, wallet_address):
    """Get the balance of a wallet address"""
    try:
        balance = w3.eth.get_balance(wallet_address)
        balance_eth = w3.from_wei(balance, 'ether')
        return balance_eth
    except Exception as e:
        print(f"Error getting balance: {e}")
        return None

def main():
    # Connect to blockchain
    w3 = connect_to_blockchain()
    
    if w3:
        # Get wallet balance
        balance = get_wallet_balance(w3, WALLET_ADDRESS)
        if balance is not None:
            print(f"Balance: {balance} ETH")
        
        # TODO: Add contract interaction functions
        # TODO: Add transaction sending functions

if __name__ == "__main__":
    main()
