from web3 import Web3

# Configuration
RPC_URL = "http://*************:24376/051b6a42-cf72-4bb0-85a7-7f838f931466"
PRIVATE_KEY = "7c6715808fa7f155c5f7ee985b235689b852da7d35dbf3b8093e4f3f3657244c"
WALLET_ADDRESS = "******************************************"
CONTRACT_ADDRESS = "******************************************"

def connect_to_blockchain():
    """Connect to the blockchain node"""
    w3 = Web3(Web3.HTTPProvider(RPC_URL))
    
    if w3.is_connected():
        print("Connected to blockchain node")
        return w3
    else:
        print("Connection failed")
        return None

def get_wallet_balance(w3, wallet_address):
    """Get the balance of a wallet address"""
    try:
        balance = w3.eth.get_balance(wallet_address)
        balance_eth = w3.from_wei(balance, 'ether')
        return balance_eth
    except Exception as e:
        print(f"Error getting balance: {e}")
        return None

def read_contract_storage(w3, contract_address, slot_count=20):
    """Read contract storage slots to find potential flags"""
    print(f"\n=== Reading Contract Storage ===")
    print(f"Contract Address: {contract_address}")

    for i in range(slot_count):
        try:
            storage_value = w3.eth.get_storage_at(contract_address, i)
            if storage_value != b'\x00' * 32:  # Skip empty slots
                hex_value = storage_value.hex()
                # Try to decode as string
                try:
                    decoded = bytes.fromhex(hex_value.replace('0x', '')).decode('utf-8', errors='ignore')
                    print(f"Slot {i}: {hex_value}")
                    if decoded.strip():
                        print(f"  Decoded: {decoded.strip()}")
                except:
                    print(f"Slot {i}: {hex_value}")
        except Exception as e:
            print(f"Error reading slot {i}: {e}")

def get_contract_code(w3, contract_address):
    """Get contract bytecode"""
    print(f"\n=== Contract Code ===")
    try:
        code = w3.eth.get_code(contract_address)
        print(f"Contract has code: {len(code)} bytes")
        return code
    except Exception as e:
        print(f"Error getting contract code: {e}")
        return None

def search_for_flag_in_transactions(w3, contract_address, block_count=100):
    """Search for flag in recent transactions"""
    print(f"\n=== Searching Transactions ===")
    try:
        latest_block = w3.eth.block_number
        print(f"Latest block: {latest_block}")

        for block_num in range(max(0, latest_block - block_count), latest_block + 1):
            try:
                block = w3.eth.get_block(block_num, full_transactions=True)
                for tx in block.transactions:
                    if tx.to and tx.to.lower() == contract_address.lower():
                        print(f"Transaction to contract: {tx.hash.hex()}")
                        # Check transaction input data
                        if tx.input and len(tx.input) > 2:
                            input_data = tx.input.hex()
                            print(f"  Input data: {input_data}")
                            # Try to decode as string
                            try:
                                decoded = bytes.fromhex(input_data.replace('0x', '')).decode('utf-8', errors='ignore')
                                if 'flag' in decoded.lower() or 'ctf' in decoded.lower():
                                    print(f"  Potential flag in input: {decoded}")
                            except:
                                pass
            except Exception as e:
                continue
    except Exception as e:
        print(f"Error searching transactions: {e}")

def try_common_contract_functions(w3, contract_address):
    """Try calling common functions that might return flags"""
    print(f"\n=== Trying Common Functions ===")

    # Common function signatures for flags
    common_functions = [
        "0x8da5cb5b",  # owner()
        "0x06fdde03",  # name()
        "0x95d89b41",  # symbol()
        "0x18160ddd",  # totalSupply()
        "0x70a08231",  # balanceOf(address)
        "0xa9059cbb",  # transfer(address,uint256)
        "0x23b872dd",  # transferFrom(address,address,uint256)
        "0x095ea7b3",  # approve(address,uint256)
        "0xdd62ed3e",  # allowance(address,address)
    ]

    for func_sig in common_functions:
        try:
            # Try calling function with no parameters
            result = w3.eth.call({
                'to': contract_address,
                'data': func_sig
            })
            if result and result != b'\x00' * 32:
                hex_result = result.hex()
                print(f"Function {func_sig}: {hex_result}")
                # Try to decode as string
                try:
                    decoded = bytes.fromhex(hex_result.replace('0x', '')).decode('utf-8', errors='ignore')
                    if decoded.strip():
                        print(f"  Decoded: {decoded.strip()}")
                except:
                    pass
        except Exception as e:
            continue

def main():
    # Connect to blockchain
    w3 = connect_to_blockchain()

    if w3:
        # Get wallet balance
        balance = get_wallet_balance(w3, WALLET_ADDRESS)
        if balance is not None:
            print(f"Balance: {balance} ETH")

        print(f"\n{'='*50}")
        print("SEARCHING FOR FLAG...")
        print(f"{'='*50}")

        # Search for flag in multiple ways
        read_contract_storage(w3, CONTRACT_ADDRESS)
        get_contract_code(w3, CONTRACT_ADDRESS)
        try_common_contract_functions(w3, CONTRACT_ADDRESS)
        search_for_flag_in_transactions(w3, CONTRACT_ADDRESS)

if __name__ == "__main__":
    main()
